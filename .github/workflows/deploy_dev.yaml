name: CI - Deploy Dev Cluster

on:
  push:
    branches: [dev]

jobs:
  deploy:
    runs-on: ubuntu-latest
    env:
      APPS: migrate backend frontend
      IMAGE_PREFIX: ghcr.io/${{ github.repository_owner }}/warda

    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Log in to GitHub Container Registry (GHCR)
        uses: docker/login-action@v3
        with:
          registry: ghcr.io
          username: ${{ github.actor }}
          password: ${{ secrets.GITHUB_TOKEN }}

      - name: Build and push all images
        run: |
          for app in $APPS; do
            echo "🛠️ Building and pushing $app..."
            docker buildx build \
              --file apps/$app/Dockerfile \
              --tag $IMAGE_PREFIX/$app:dev \
              --push \
              .  # The dot at the end is important - it sets the build context to the repository root
          done
      - name: Set up Helm
        run: |
          curl -fsSL https://raw.githubusercontent.com/helm/helm/main/scripts/get-helm-3 | bash

      - name: Helm deploy
        run: |
          helm upgrade --install warda helm/warda \
            --namespace osint \
            --create-namespace \
            --set image.backend.repository=$IMAGE_PREFIX/backend \
            --set image.backend.tag=dev \
            --set image.frontend.repository=$IMAGE_PREFIX/frontend \
            --set image.frontend.tag=dev \
            --set image.migrate.repository=$IMAGE_PREFIX/migrate \
            --set image.migrate.tag=dev