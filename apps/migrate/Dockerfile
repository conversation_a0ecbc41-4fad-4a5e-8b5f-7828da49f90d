# Use the official Rust image for building
FROM rust:1.88.0-slim-bookworm AS builder

# Install system dependencies
RUN apt-get update && apt-get install -y pkg-config libssl-dev && rm -rf /var/lib/apt/lists/*

WORKDIR /app

# Copy only migrate-specific files for isolated build
COPY apps/migrate/Cargo.toml ./
COPY apps/migrate/src ./src/

# Create a dummy main.rs for dependency caching
RUN echo 'fn main() {}' > src/main.rs

# Build dependencies only
RUN cargo build --release

# Copy the actual source code
COPY apps/migrate/src ./src/

# Build the actual application
RUN cargo build --release

# Final image
FROM debian:bookworm-slim AS runtime

# Install runtime dependencies
RUN apt-get update && apt-get install -y ca-certificates postgresql-client && rm -rf /var/lib/apt/lists/*

# Copy the binary from the builder
COPY --from=builder /app/target/release/migrate /usr/local/bin/

# Set the entrypoint
ENTRYPOINT ["/usr/local/bin/migrate"]
