# Use the official Rust image for building
FROM rust:1.88.0-slim-bookworm AS builder

# Install system dependencies
RUN apt-get update && apt-get install -y pkg-config libssl-dev && rm -rf /var/lib/apt/lists/*

# Create and set the working directory
WORKDIR /app

# Copy only the necessary files for dependency resolution
ARG ROOT=.
COPY ${ROOT}/crates/shared crates/shared
COPY ${ROOT}/apps/migrate/Cargo.toml apps/migrate/
COPY ${ROOT}/apps/migrate/src apps/migrate/src
COPY ${ROOT}/apps/migrate/migrations apps/migrate/migratio

# Create a dummy main.rs for initial build
RUN mkdir -p apps/migrate/src && \
    echo 'fn main() {}' > apps/migrate/src/main.rs

# Change to the migrate directory for building
WORKDIR /app/apps/migrate

# Do an initial build to cache dependencies
RUN cargo build --release

# Now copy the actual source code and migrations
COPY ${ROOT}/crates/shared crates/shared
COPY ${ROOT}/apps/migrate/src ./src/
COPY ${ROOT}/apps/migrate/migrations ./migrations/

# Touch the main file to force a rebuild
RUN touch src/main.rs

# Build the application
RUN cargo build --release

# Final image
FROM debian:bookworm-slim AS runtime

# Install runtime dependencies
RUN apt-get update && apt-get install -y ca-certificates postgresql-client && rm -rf /var/lib/apt/lists/*

# Copy the binary from the builder
COPY --from=builder /app/apps/migrate/target/release/migrate /usr/local/bin/

# Set the entrypoint
ENTRYPOINT ["/usr/local/bin/migrate"]
