FROM rust:1.88.0-slim-bookworm AS builder

RUN apt-get update && apt-get install -y pkg-config libssl-dev && rm -rf /var/lib/apt/lists/*

WORKDIR /app

# Copy workspace configuration and all Cargo.toml files
COPY Cargo.toml ./
COPY Cargo.lock ./
COPY rust-toolchain.toml ./
COPY apps/backend/Cargo.toml ./apps/backend/
COPY crates/shared/Cargo.toml ./crates/shared/

# Create dummy source files for dependency caching
RUN mkdir -p apps/backend/src && \
    echo 'fn main() {}' > apps/backend/src/main.rs
RUN mkdir -p crates/shared/src && \
    echo '' > crates/shared/src/lib.rs

# Build dependencies only
WORKDIR /app/apps/backend
RUN cargo build --release

# Copy the actual source code
COPY . /app/

# Build the actual application
RUN cargo build --release

# Final stage
FROM debian:bookworm-slim
RUN apt-get update && apt-get install -y ca-certificates && rm -rf /var/lib/apt/lists/*
COPY --from=builder /app/target/release/backend /usr/local/bin/
ENTRYPOINT ["/usr/local/bin/backend"]