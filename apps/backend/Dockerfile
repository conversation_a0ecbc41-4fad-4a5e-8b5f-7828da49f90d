FROM rust:1.88.0-slim-bookworm AS builder

RUN apt-get update && apt-get install -y pkg-config libssl-dev && rm -rf /var/lib/apt/lists/*

WORKDIR /app

# Copy workspace configuration
COPY Cargo.toml Cargo.lock ./
COPY rust-toolchain.toml ./

# Copy all Cargo.toml files and crates
COPY apps/backend/Cargo.toml ./apps/backend/
COPY crates ./crates/

# Create dummy source files for dependency caching
RUN mkdir -p apps/backend/src && \
    echo 'fn main() {}' > apps/backend/src/main.rs

# Build dependencies only
WORKDIR /app/apps/backend
RUN cargo build --release

# Copy the actual source code
COPY . /app/

# Build the actual application
RUN cargo build --release

# Final stage
FROM debian:bookworm-slim
RUN apt-get update && apt-get install -y ca-certificates && rm -rf /var/lib/apt/lists/*
COPY --from=builder /app/target/release/backend /usr/local/bin/
ENTRYPOINT ["/usr/local/bin/backend"]