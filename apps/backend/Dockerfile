FROM rust:1.88.0-slim-bookworm AS builder

RUN apt-get update && apt-get install -y pkg-config libssl-dev && rm -rf /var/lib/apt/lists/*

# Create a dummy project to cache dependencies
RUN USER=root cargo new --bin dummy
WORKDIR /usr/src/app

# Create necessary directories
COPY crates/shared/Cargo.toml ./crates/shared/
COPY apps/backend/Cargo.toml ./apps/backend/
COPY apps/backend/Cargo.lock ./apps/backend/

# Build dependencies
RUN cargo build --release

# Copy the actual source code
COPY . .

# Move to the real source code
WORKDIR /usr/src/app/apps/backend

# Build the actual application
RUN cargo build --release

# Final stage
FROM debian:bookworm-slim
RUN apt-get update && apt-get install -y ca-certificates && rm -rf /var/lib/apt/lists/*
COPY --from=builder /usr/src/app/target/release/backend /usr/local/bin/
ENTRYPOINT ["/usr/local/bin/backend"]