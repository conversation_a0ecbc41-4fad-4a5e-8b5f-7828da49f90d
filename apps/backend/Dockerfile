FROM rust:1.88.0-slim-bookworm AS builder

RUN apt-get update && apt-get install -y pkg-config libssl-dev && rm -rf /var/lib/apt/lists/*

WORKDIR /app

# Copy workspace configuration
COPY Cargo.toml ./
COPY rust-toolchain.toml ./

# Copy all workspace member Cargo.toml files
COPY apps/backend/Cargo.toml ./apps/backend/
COPY apps/migrate/Cargo.toml ./apps/migrate/

# Create dummy shared crate and frontend Cargo.toml files
RUN mkdir -p crates/shared && \
    echo '[package]' > crates/shared/Cargo.toml && \
    echo 'name = "shared"' >> crates/shared/Cargo.toml && \
    echo 'version = "0.1.0"' >> crates/shared/Cargo.toml && \
    echo 'edition = "2021"' >> crates/shared/Cargo.toml && \
    echo '[dependencies]' >> crates/shared/Cargo.toml && \
    echo 'serde = { version = "1.0", features = ["derive"] }' >> crates/shared/Cargo.toml

RUN mkdir -p apps/frontend && \
    echo '[package]' > apps/frontend/Cargo.toml && \
    echo 'name = "frontend"' >> apps/frontend/Cargo.toml && \
    echo 'version = "0.1.0"' >> apps/frontend/Cargo.toml && \
    echo 'edition = "2021"' >> apps/frontend/Cargo.toml

# Create dummy source files for all workspace members
RUN mkdir -p apps/backend/src && \
    echo 'fn main() {}' > apps/backend/src/main.rs && \
    mkdir -p apps/migrate/src && \
    echo 'fn main() {}' > apps/migrate/src/main.rs && \
    mkdir -p apps/frontend/src && \
    echo 'fn main() {}' > apps/frontend/src/main.rs && \
    mkdir -p crates/shared/src && \
    echo '' > crates/shared/src/lib.rs

# Build dependencies only
WORKDIR /app/apps/backend
RUN cargo build --release

# Copy the actual source code
COPY . /app/

# Build the actual application
RUN cargo build --release

# Final stage
FROM debian:bookworm-slim
RUN apt-get update && apt-get install -y ca-certificates && rm -rf /var/lib/apt/lists/*
COPY --from=builder /app/target/release/backend /usr/local/bin/
ENTRYPOINT ["/usr/local/bin/backend"]