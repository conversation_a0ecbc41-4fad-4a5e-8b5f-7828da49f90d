#![warn(clippy::all, rust_2018_idioms)]
#![cfg_attr(not(debug_assertions), windows_subsystem = "windows")]

// When compiling natively:
#[cfg(not(target_arch = "wasm32"))]
fn main() -> eframe::Result {
    // Initialize logging for native
    tracing_subscriber::fmt()
        .with_env_filter(
            tracing_subscriber::EnvFilter::try_from_default_env()
                .unwrap_or_else(|_| "frontend=debug,walkers=info".into()),
        )
        .init();

    tracing::info!("Starting frontend application");

    let native_options = eframe::NativeOptions {
        viewport: egui::ViewportBuilder::default()
            .with_inner_size([400.0, 300.0])
            .with_min_inner_size([300.0, 220.0])
            .with_icon(
                eframe::icon_data::from_png_bytes(&include_bytes!("../assets/icon-256.png")[..])
                    .expect("Failed to load icon"),
            ),
        ..Default::default()
    };
    
    eframe::run_native(
        "eframe template",
        native_options,
        Box::new(|cc| {
            tracing::debug!("Creating TemplateApp");
            Ok(Box::new(frontend::TemplateApp::new(cc)))
        }),
    )
}

// When compiling to web using trunk:
#[cfg(target_arch = "wasm32")]
fn main() {
    console_log::init_with_level(log::Level::Debug).expect("Failed to initialize logger");
    log::info!("Starting frontend web application");

    let web_options = eframe::WebOptions::default();

    wasm_bindgen_futures::spawn_local(async {
        let document = web_sys::window()
            .expect("No window")
            .document()
            .expect("No document");

        let canvas = document
            .get_element_by_id("the_canvas_id")
            .unwrap_or_else(|| {
                let c = document.create_element("canvas").unwrap();
                c.set_id("the_canvas_id");
                document.body().unwrap().append_child(&c).unwrap();
                c
            });

        let loading_text = document
            .get_element_by_id("loading-text")
            .expect("No loading-text element");

        let start_result = eframe::start_web(
            "the_canvas_id",
            web_options,
            Box::new(|cc| {
                let app = Box::new(frontend::TemplateApp::new(cc));
                let app_ptr: *mut frontend::TemplateApp = &*app;
                frontend::register_app_instance(app_ptr);
                app
            }),
        ).await;

        match start_result {
            Ok(_) => {
                log::info!("Frontend started successfully");
                loading_text.remove();
            }
            Err(e) => {
                log::error!("Failed to start frontend: {:?}", e);
                loading_text.set_inner_html(
                    "<p> The app has crashed. See the developer console for details. </p>",
                );
                panic!("Failed to start eframe: {e:?}");
            }
        }
    });
}
