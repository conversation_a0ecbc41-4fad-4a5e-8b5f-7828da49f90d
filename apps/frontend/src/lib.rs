#![warn(clippy::all, rust_2018_idioms)]

mod app;

use wasm_bindgen::prelude::*;

// Use a static mutable pointer for the WASM app instance
static mut APP_INSTANCE: Option<*mut app::TemplateApp> = None;

#[wasm_bindgen]
pub fn register_app_instance(ptr: *mut app::TemplateApp) {
    unsafe {
        APP_INSTANCE = Some(ptr);
    }
}

#[wasm_bindgen]
pub fn update_auth_state() {
    unsafe {
        if let Some(ptr) = APP_INSTANCE {
            let app = &mut *ptr;
            app.shared_data.auth_state.update_auth_state();
        }
    }
}

pub use app::TemplateApp;