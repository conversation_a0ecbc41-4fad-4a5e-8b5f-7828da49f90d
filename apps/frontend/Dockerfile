FROM rust:1.88-slim AS builder

RUN apt-get update && apt-get install -y \
    build-essential \
    pkg-config \
    libssl-dev && rm -rf /var/lib/apt/lists/*

RUN rustup target add wasm32-unknown-unknown && \
    cargo install --locked trunk wasm-bindgen-cli

ENV RUSTFLAGS='--cfg getrandom_backend="wasm_js"'
WORKDIR /app

# COPY shared and frontend app
COPY ./crates/shared crates/shared
COPY ./apps/frontend apps/frontend

WORKDIR /app/apps/frontend

# Dummy files for dependency caching
RUN mkdir -p src && \
    echo "fn main() {}" > src/main.rs && \
    echo "pub mod app; pub use app::TemplateApp;" > src/lib.rs && \
    echo "pub struct TemplateApp; impl eframe::App for TemplateApp { fn update(&mut self, _: &egui::Context, _: &mut eframe::Frame) {} }" > src/app.rs

RUN cargo build --target=wasm32-unknown-unknown --release || true

# Actual build
RUN touch src/main.rs src/lib.rs && trunk build --release

FROM nginx:alpine
COPY --from=builder /app/apps/frontend/dist /usr/share/nginx/html
COPY apps/frontend/nginx.conf /etc/nginx/conf.d/default.conf
COPY apps/frontend/entrypoint.sh /entrypoint.sh
RUN chmod +x /entrypoint.sh
EXPOSE 80
ENTRYPOINT ["/entrypoint.sh"]