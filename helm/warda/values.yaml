# Global configuration
global:
  postgresql:
    postgresqlUsername: postgres
    postgresqlPassword: postgres_password
    postgresqlDatabase: postgresql
    service:
      port: 5432
    persistence:
      enabled: true
      size: 8Gi

# PostgreSQL chart configuration
postgresql:
  enabled: true
  # Use values from global configuration
  postgresqlUsername: postgres
  postgresqlPassword: postgres_password
  postgresqlDatabase: postgresql
  service:
    port: 5432
  persistence:
    enabled: true
    size: 8Gi

# Keycloak configuration
keycloak:
  enabled: true

  # Image configuration
  image:
    repository: quay.io/keycloak/keycloak
    tag: "23.0.0"
    pullPolicy: IfNotPresent

  # Admin credentials
  admin:
    username: admin
    password: admin123

  # Database configuration
  database:
    host: warda-postgresql
    port: 5432
    name: keycloak
    username: keycloak
    password: keycloak_password

  # Hostname configuration
  hostname: keycloak.local

  # Ingress configuration
  ingress:
    enabled: true
    className: ""
    annotations:
      nginx.ingress.kubernetes.io/proxy-buffer-size: "128k"
      nginx.ingress.kubernetes.io/proxy-buffers-number: "4"
    hosts:
      - host: keycloak.local
        paths:
          - path: /
            pathType: Prefix
    tls: []

  # Resource configuration
  resources:
    limits:
      cpu: 1000m
      memory: 1Gi
    requests:
      cpu: 500m
      memory: 512Mi

  # Realm and client configuration
  realm:
    name: warda
    displayName: "Warda Platform"
    enabled: true
    frontendClient:
      clientId: warda-frontend
      name: "Warda Frontend"
      enabled: true
      publicClient: true
      directAccessGrantsEnabled: false
      standardFlowEnabled: true
      implicitFlowEnabled: false
      serviceAccountsEnabled: false
      redirectUris:
        - "http://frontend.local/*"
      webOrigins:
        - "http://frontend.local"
    backendClient:
      clientId: warda-backend
      name: "Warda Backend"
      enabled: true
      publicClient: false
      directAccessGrantsEnabled: true
      standardFlowEnabled: true
      serviceAccountsEnabled: true
      secret: backend-client-secret
      redirectUris:
        - "http://localhost:8080/*"
        - "http://backend.local/*"

frontend:
  enabled: true
  # Mapbox configuration
  mapboxToken: "pk.eyJ1Ijoic2lldHNlbSIsImEiOiJjbWR1M2NmajIxNXNxMmtyMzIzenZwbW1mIn0.isXf_tnxfA4aBS3NeJxJkA"
  # Keycloak configuration
  keycloak:
    url: "http://keycloak.local"
    realm: "warda"
    clientId: "warda-frontend"

# Backend configuration
backend:
  enabled: true
  # Database URL for the backend
  databaseUrl: "***********************************************************/postgresql"

  image:
    repository: warda/backend
    tag: latest
    pullPolicy: IfNotPresent
  env:
    RUST_LOG: "warda=debug,backend=debug,tower_http=debug"
  # Database configuration
  database:
    host: "warda-postgresql"
    port: 5432
    name: postgresql
    user: postgres
  # OAuth/Keycloak configuration
  oauth:
    keycloak:
      url: "http://keycloak:8080"
      realm: "warda"
      clientId: "warda-backend"
      issuer: "http://keycloak:8080/realms/warda"

# Migrate configuration
migrate:
  enabled: true
  # Use a separate image for migrations
  image:
    repository: warda/migrate
    tag: latest
    pullPolicy: IfNotPresent
  # Wait container configuration
  waitContainer:
    image: alpine
    tag: "3.18"