# Default values for frontend.

replicaCount: 1

image:
  repository: warda/frontend
  pullPolicy: Never
  tag: latest

imagePullSecrets: []
nameOverride: ""
fullnameOverride: "frontend"

service:
  type: NodePort
  port: 80
  nodePort: 30088

ingress:
  enabled: true
  host: frontend.local
  annotations: {}
  tls: false
  # tlsSecretName: frontend-tls

resources:
  limits:
    cpu: 100m
    memory: 128Mi
  requests:
    cpu: 50m
    memory: 64Mi

autoscaling:
  enabled: false
  minReplicas: 1
  maxReplicas: 3
  targetCPUUtilizationPercentage: 80
  targetMemoryUtilizationPercentage: 80

nodeSelector: {}

tolerations: []

affinity: {}
