# Default values for backend.
# This is a YAML-formatted file.

# Replica count
replicaCount: 1

# Image configuration
image:
  repository: warda/backend
  tag: latest
  pullPolicy: Never

# Wait container configuration
waitContainer:
  image: alpine
  tag: "3.18"

# Pod configuration
podAnnotations: {}
podLabels: {}

# Security context
podSecurityContext: {}
securityContext: {}

# Service account configuration
serviceAccount:
  create: true
  annotations: {}
  name: ""

# Image pull secrets
imagePullSecrets: []

# Application environment variables
env:
  RUST_LOG: "warda=info,backend=info,tower_http=info"
  CONFIG_PATH: /config/Config.toml

# Database configuration
database:
  host: "postgresql"
  port: 5432
  name: postgresql
  user: postgres

# Resource limits and requests
resources:
  limits:
    cpu: 1000m
    memory: 1Gi
  requests:
    cpu: 100m
    memory: 128Mi

# Volume configuration
volumeMount:
  configPath: /config

# Node selector, affinity, and tolerations for pod assignment
nodeSelector: {}
affinity: {}
tolerations: []

# Service configuration
service:
  type: ClusterIP
  port: 80
  targetPort: http
  nodePort: null
  annotations: {}

# Ingress configuration
ingress:
  enabled: false
  className: ""
  annotations: {}
  hosts:
    - host: chart-example.local
      paths:
        - path: /
          pathType: ImplementationSpecific
  tls: []
